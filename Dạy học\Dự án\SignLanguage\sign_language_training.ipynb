{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Sign Language Recognition Training\n", "## Sử dụng CNN và MediaPipe để nhận diện ngôn ngữ cử chỉ tay\n", "\n", "Dataset: Sign MNIST (A-Z trừ J và Z)\n", "- Training: 27,455 samples\n", "- Test: 7,172 samples\n", "- Image size: 28x28 pixels"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import c<PERSON><PERSON> thư viện c<PERSON>n thiết\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import classification_report, confusion_matrix\n", "\n", "# Deep Learning\n", "import tensorflow as tf\n", "from tensorflow import keras\n", "from tensorflow.keras import layers\n", "from tensorflow.keras.utils import to_categorical\n", "\n", "# MediaPipe\n", "import mediapipe as mp\n", "import cv2\n", "\n", "# Utilities\n", "import os\n", "import pickle\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "print(\"TensorFlow version:\", tf.__version__)\n", "print(\"GPU Available:\", tf.config.list_physical_devices('GPU'))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# <PERSON><PERSON><PERSON> dữ liệu\n", "print(\"<PERSON><PERSON> đọc dữ liệu...\")\n", "train_df = pd.read_csv('sign_mnist_train/sign_mnist_train.csv')\n", "test_df = pd.read_csv('sign_mnist_test/sign_mnist_test.csv')\n", "\n", "print(f\"Training data shape: {train_df.shape}\")\n", "print(f\"Test data shape: {test_df.shape}\")\n", "\n", "# <PERSON><PERSON>n thị thông tin về labels\n", "print(\"\\nPhân bố labels trong training set:\")\n", "print(train_df['label'].value_counts().sort_index())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Mapping labels sang chữ cái\n", "# Note: J (9) v<PERSON> <PERSON> (25) không có trong dataset vì cần chuyển động\n", "label_map = {\n", "    0: 'A', 1: 'B', 2: 'C', 3: 'D', 4: 'E', 5: 'F', 6: 'G', 7: 'H', 8: 'I',\n", "    10: '<PERSON>', 11: '<PERSON>', 12: 'M', 13: 'N', 14: 'O', 15: 'P', 16: 'Q', 17: 'R',\n", "    18: 'S', 19: 'T', 20: 'U', 21: 'V', 22: 'W', 23: 'X', 24: 'Y'\n", "}\n", "\n", "print(\"Label mapping:\")\n", "for label, letter in label_map.items():\n", "    print(f\"{label}: {letter}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# <PERSON><PERSON><PERSON> bị dữ liệu\n", "def prepare_data(df):\n", "    # Tách labels và pixels\n", "    labels = df['label'].values\n", "    pixels = df.drop('label', axis=1).values\n", "    \n", "    # Reshape pixels thành ảnh 28x28\n", "    images = pixels.reshape(-1, 28, 28, 1)\n", "    \n", "    # Normalize pixel values\n", "    images = images.astype('float32') / 255.0\n", "    \n", "    return images, labels\n", "\n", "# Chuẩn bị training và test data\n", "X_train, y_train = prepare_data(train_df)\n", "X_test, y_test = prepare_data(test_df)\n", "\n", "print(f\"X_train shape: {X_train.shape}\")\n", "print(f\"y_train shape: {y_train.shape}\")\n", "print(f\"X_test shape: {X_test.shape}\")\n", "print(f\"y_test shape: {y_test.shape}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# <PERSON><PERSON><PERSON> thị một số ảnh mẫu\n", "plt.figure(figsize=(15, 8))\n", "for i in range(20):\n", "    plt.subplot(4, 5, i+1)\n", "    plt.imshow(X_train[i].reshape(28, 28), cmap='gray')\n", "    plt.title(f'Label: {y_train[i]} ({label_map.get(y_train[i], \"Unknown\")})')\n", "    plt.axis('off')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Tạo validation set từ training data\n", "X_train_split, X_val, y_train_split, y_val = train_test_split(\n", "    X_train, y_train, test_size=0.2, random_state=42, stratify=y_train\n", ")\n", "\n", "print(f\"Training set: {X_train_split.shape[0]} samples\")\n", "print(f\"Validation set: {X_val.shape[0]} samples\")\n", "print(f\"Test set: {X_test.shape[0]} samples\")\n", "\n", "# Số lượng classes\n", "num_classes = len(np.unique(y_train))\n", "print(f\"Number of classes: {num_classes}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Tạo CNN model\n", "def create_cnn_model(input_shape, num_classes):\n", "    model = keras.Sequential([\n", "        # First Convolutional Block\n", "        layers.Conv2D(32, (3, 3), activation='relu', input_shape=input_shape),\n", "        layers.BatchNormalization(),\n", "        layers.Conv2D(32, (3, 3), activation='relu'),\n", "        layers.MaxPooling2D((2, 2)),\n", "        layers.Dropout(0.25),\n", "        \n", "        # Second Convolutional Block\n", "        layers.Conv2D(64, (3, 3), activation='relu'),\n", "        layers.BatchNormalization(),\n", "        layers.Conv2D(64, (3, 3), activation='relu'),\n", "        layers.MaxPooling2D((2, 2)),\n", "        layers.Dropout(0.25),\n", "        \n", "        # Third Convolutional Block\n", "        layers.Conv2D(128, (3, 3), activation='relu'),\n", "        layers.BatchNormalization(),\n", "        layers.Dropout(0.25),\n", "        \n", "        # Flatten and Dense layers\n", "        layers.<PERSON><PERSON>(),\n", "        layers.Dense(512, activation='relu'),\n", "        layers.BatchNormalization(),\n", "        layers.Dropout(0.5),\n", "        layers.Dense(256, activation='relu'),\n", "        layers.Dropout(0.5),\n", "        layers.Dense(num_classes, activation='softmax')\n", "    ])\n", "    \n", "    return model\n", "\n", "# Tạo model\n", "model = create_cnn_model((28, 28, 1), num_classes)\n", "model.summary()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Compile model\n", "model.compile(\n", "    optimizer='adam',\n", "    loss='sparse_categorical_crossentropy',\n", "    metrics=['accuracy']\n", ")\n", "\n", "# Callbacks\n", "callbacks = [\n", "    keras.callbacks.EarlyStopping(\n", "        monitor='val_loss',\n", "        patience=10,\n", "        restore_best_weights=True\n", "    ),\n", "    keras.callbacks.ReduceLROnPlateau(\n", "        monitor='val_loss',\n", "        factor=0.2,\n", "        patience=5,\n", "        min_lr=0.0001\n", "    ),\n", "    keras.callbacks.ModelCheckpoint(\n", "        'best_sign_model.h5',\n", "        monitor='val_accuracy',\n", "        save_best_only=True,\n", "        verbose=1\n", "    )\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Training model\n", "print(\"Bắt đầu training...\")\n", "history = model.fit(\n", "    X_train_split, y_train_split,\n", "    batch_size=32,\n", "    epochs=50,\n", "    validation_data=(X_val, y_val),\n", "    callbacks=callbacks,\n", "    verbose=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Vẽ training history\n", "plt.figure(figsize=(15, 5))\n", "\n", "plt.subplot(1, 2, 1)\n", "plt.plot(history.history['accuracy'], label='Training Accuracy')\n", "plt.plot(history.history['val_accuracy'], label='Validation Accuracy')\n", "plt.title('Model Accuracy')\n", "plt.xlabel('Epoch')\n", "plt.ylabel('Accuracy')\n", "plt.legend()\n", "\n", "plt.subplot(1, 2, 2)\n", "plt.plot(history.history['loss'], label='Training Loss')\n", "plt.plot(history.history['val_loss'], label='Validation Loss')\n", "plt.title('Model Loss')\n", "plt.xlabel('Epoch')\n", "plt.ylabel('Loss')\n", "plt.legend()\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Đánh giá model trên test set\n", "print(\"Đánh giá model trên test set...\")\n", "test_loss, test_accuracy = model.evaluate(X_test, y_test, verbose=0)\n", "print(f\"Test Accuracy: {test_accuracy:.4f}\")\n", "print(f\"Test Loss: {test_loss:.4f}\")\n", "\n", "# Predictions\n", "y_pred = model.predict(X_test)\n", "y_pred_classes = np.argmax(y_pred, axis=1)\n", "\n", "# Classification report\n", "print(\"\\nClassification Report:\")\n", "print(classification_report(y_test, y_pred_classes))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Confusion Matrix\n", "plt.figure(figsize=(12, 10))\n", "cm = confusion_matrix(y_test, y_pred_classes)\n", "sns.heatmap(cm, annot=True, fmt='d', cmap='Blues')\n", "plt.title('Confusion Matrix')\n", "plt.xlabel('Predicted')\n", "plt.ylabel('Actual')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Lưu model và các thông tin cần thiết\n", "print(\"<PERSON><PERSON> model...\")\n", "\n", "# Lưu model architecture và weights\n", "model.save('sign_language_model.h5')\n", "print(\"Đã lưu model: sign_language_model.h5\")\n", "\n", "# Lưu label mapping\n", "with open('label_mapping.pkl', 'wb') as f:\n", "    pickle.dump(label_map, f)\n", "print(\"Đã lưu label mapping: label_mapping.pkl\")\n", "\n", "# Lưu model info\n", "model_info = {\n", "    'input_shape': (28, 28, 1),\n", "    'num_classes': num_classes,\n", "    'test_accuracy': test_accuracy,\n", "    'label_mapping': label_map\n", "}\n", "\n", "with open('model_info.pkl', 'wb') as f:\n", "    pickle.dump(model_info, f)\n", "print(\"Đã lưu model info: model_info.pkl\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## MediaPipe Integration\n", "### <PERSON><PERSON><PERSON> bị cho việc sử dụng MediaPipe để detect hand landmarks"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# MediaPipe Hand Detection\n", "class HandDetector:\n", "    def __init__(self):\n", "        self.mp_hands = mp.solutions.hands\n", "        self.hands = self.mp_hands.Hands(\n", "            static_image_mode=False,\n", "            max_num_hands=1,\n", "            min_detection_confidence=0.7,\n", "            min_tracking_confidence=0.5\n", "        )\n", "        self.mp_draw = mp.solutions.drawing_utils\n", "    \n", "    def detect_hands(self, image):\n", "        # Convert BGR to RGB\n", "        rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)\n", "        results = self.hands.process(rgb_image)\n", "        return results\n", "    \n", "    def extract_hand_region(self, image, results):\n", "        if results.multi_hand_landmarks:\n", "            hand_landmarks = results.multi_hand_landmarks[0]\n", "            \n", "            # Get bounding box coordinates\n", "            h, w, _ = image.shape\n", "            x_coords = [landmark.x * w for landmark in hand_landmarks.landmark]\n", "            y_coords = [landmark.y * h for landmark in hand_landmarks.landmark]\n", "            \n", "            x_min, x_max = int(min(x_coords)), int(max(x_coords))\n", "            y_min, y_max = int(min(y_coords)), int(max(y_coords))\n", "            \n", "            # Add padding\n", "            padding = 20\n", "            x_min = max(0, x_min - padding)\n", "            y_min = max(0, y_min - padding)\n", "            x_max = min(w, x_max + padding)\n", "            y_max = min(h, y_max + padding)\n", "            \n", "            # Extract hand region\n", "            hand_region = image[y_min:y_max, x_min:x_max]\n", "            return hand_region, (x_min, y_min, x_max, y_max)\n", "        \n", "        return None, None\n", "\n", "# Test MediaPipe\n", "hand_detector = HandDetector()\n", "print(\"MediaPipe Hand Detector initialized successfully!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Hàm preprocessing cho real-time prediction\n", "def preprocess_for_prediction(hand_region):\n", "    \"\"\"Preprocess hand region for model prediction\"\"\"\n", "    if hand_region is None:\n", "        return None\n", "    \n", "    # Convert to grayscale\n", "    gray = cv2.cvtColor(hand_region, cv2.COLOR_BGR2GRAY)\n", "    \n", "    # Resize to 28x28\n", "    resized = cv2.resize(gray, (28, 28))\n", "    \n", "    # Normalize\n", "    normalized = resized.astype('float32') / 255.0\n", "    \n", "    # Reshape for model input\n", "    processed = normalized.reshape(1, 28, 28, 1)\n", "    \n", "    return processed\n", "\n", "def predict_sign(model, processed_image, label_map):\n", "    \"\"\"Predict sign language letter\"\"\"\n", "    if processed_image is None:\n", "        return None, 0\n", "    \n", "    prediction = model.predict(processed_image, verbose=0)\n", "    predicted_class = np.argmax(prediction)\n", "    confidence = np.max(prediction)\n", "    \n", "    predicted_letter = label_map.get(predicted_class, 'Unknown')\n", "    \n", "    return predicted_letter, confidence\n", "\n", "print(\"Preprocessing functions defined successfully!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test với một <PERSON>nh từ test set\n", "test_idx = 0\n", "test_image = X_test[test_idx]\n", "true_label = y_test[test_idx]\n", "\n", "# Predict\n", "prediction = model.predict(test_image.reshape(1, 28, 28, 1), verbose=0)\n", "predicted_class = np.argmax(prediction)\n", "confidence = np.max(prediction)\n", "\n", "# Display\n", "plt.figure(figsize=(8, 4))\n", "plt.subplot(1, 2, 1)\n", "plt.imshow(test_image.reshape(28, 28), cmap='gray')\n", "plt.title(f'True: {label_map.get(true_label, \"Unknown\")}')\n", "plt.axis('off')\n", "\n", "plt.subplot(1, 2, 2)\n", "plt.bar(range(len(prediction[0])), prediction[0])\n", "plt.title(f'Predicted: {label_map.get(predicted_class, \"Unknown\")} ({confidence:.3f})')\n", "plt.xlabel('Class')\n", "plt.ylabel('Probability')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(f\"True label: {true_label} ({label_map.get(true_label, 'Unknown')})\")\n", "print(f\"Predicted: {predicted_class} ({label_map.get(predicted_class, 'Unknown')})\")\n", "print(f\"Confidence: {confidence:.4f}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}