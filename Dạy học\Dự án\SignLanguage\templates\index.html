<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Language Recognition</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background-color: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .video-container {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
        }
        
        #video {
            border: 3px solid #007bff;
            border-radius: 10px;
            max-width: 100%;
        }
        
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 0 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        
        button:hover {
            background-color: #0056b3;
        }
        
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        
        .prediction-area {
            background-color: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        
        .prediction-letter {
            font-size: 48px;
            font-weight: bold;
            color: #007bff;
            margin: 10px 0;
        }
        
        .confidence {
            font-size: 18px;
            color: #6c757d;
        }
        
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            text-align: center;
        }
        
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .alphabet-reference {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 10px;
        }
        
        .alphabet-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(40px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }
        
        .alphabet-item {
            background-color: white;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            text-align: center;
            font-weight: bold;
        }
        
        .alphabet-item.available {
            color: #007bff;
        }
        
        .alphabet-item.unavailable {
            color: #6c757d;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤟 Sign Language Recognition</h1>
        
        <div class="video-container">
            <video id="video" width="640" height="480" autoplay></video>
        </div>
        
        <div class="controls">
            <button id="startBtn" onclick="startCamera()">Start Camera</button>
            <button id="stopBtn" onclick="stopCamera()" disabled>Stop Camera</button>
            <button id="predictBtn" onclick="captureAndPredict()" disabled>Predict Sign</button>
        </div>
        
        <div id="status" class="status"></div>
        
        <div class="prediction-area">
            <h3>Prediction Result</h3>
            <div id="predictionLetter" class="prediction-letter">-</div>
            <div id="confidence" class="confidence">Confidence: 0%</div>
        </div>
        
        <div class="alphabet-reference">
            <h3>Available Letters</h3>
            <p>The model can recognize the following letters (J and Z require motion and are not included):</p>
            <div class="alphabet-grid" id="alphabetGrid"></div>
        </div>
    </div>

    <script>
        let video = document.getElementById('video');
        let stream = null;
        let isCapturing = false;
        
        // Available letters (excluding J and Z)
        const availableLetters = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y'];
        const allLetters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.split('');
        
        // Initialize alphabet grid
        function initAlphabetGrid() {
            const grid = document.getElementById('alphabetGrid');
            allLetters.forEach(letter => {
                const item = document.createElement('div');
                item.className = 'alphabet-item';
                item.textContent = letter;
                
                if (availableLetters.includes(letter)) {
                    item.classList.add('available');
                } else {
                    item.classList.add('unavailable');
                }
                
                grid.appendChild(item);
            });
        }
        
        function showStatus(message, type = 'success') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }
        
        async function startCamera() {
            try {
                stream = await navigator.mediaDevices.getUserMedia({ 
                    video: { width: 640, height: 480 } 
                });
                video.srcObject = stream;
                
                document.getElementById('startBtn').disabled = true;
                document.getElementById('stopBtn').disabled = false;
                document.getElementById('predictBtn').disabled = false;
                
                showStatus('Camera started successfully!', 'success');
            } catch (err) {
                showStatus('Error accessing camera: ' + err.message, 'error');
            }
        }
        
        function stopCamera() {
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
                video.srcObject = null;
                stream = null;
            }
            
            document.getElementById('startBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
            document.getElementById('predictBtn').disabled = true;
            
            showStatus('Camera stopped.', 'warning');
        }
        
        function captureAndPredict() {
            if (isCapturing) return;
            
            isCapturing = true;
            document.getElementById('predictBtn').disabled = true;
            
            // Create canvas to capture frame
            const canvas = document.createElement('canvas');
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            const ctx = canvas.getContext('2d');
            ctx.drawImage(video, 0, 0);
            
            // Convert to base64
            const imageData = canvas.toDataURL('image/jpeg');
            
            // Send to server
            fetch('/predict', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ image: imageData })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (data.hand_detected) {
                        document.getElementById('predictionLetter').textContent = data.prediction || '?';
                        document.getElementById('confidence').textContent = 
                            `Confidence: ${(data.confidence * 100).toFixed(1)}%`;
                        showStatus(`Predicted: ${data.prediction} (${(data.confidence * 100).toFixed(1)}%)`, 'success');
                    } else {
                        document.getElementById('predictionLetter').textContent = '👋';
                        document.getElementById('confidence').textContent = 'No hand detected';
                        showStatus('No hand detected. Please show your hand to the camera.', 'warning');
                    }
                } else {
                    showStatus('Error: ' + data.error, 'error');
                }
            })
            .catch(error => {
                showStatus('Network error: ' + error.message, 'error');
            })
            .finally(() => {
                isCapturing = false;
                document.getElementById('predictBtn').disabled = false;
            });
        }
        
        // Initialize on page load
        window.onload = function() {
            initAlphabetGrid();
            showStatus('Click "Start Camera" to begin sign language recognition.', 'warning');
        };
    </script>
</body>
</html>
