#!/usr/bin/env python3
"""
Script để train model Sign Language Recognition
Chạy script này trư<PERSON>c khi sử dụng web app
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
import pickle
import os

# Thiết lập GPU nếu có
gpus = tf.config.experimental.list_physical_devices('GPU')
if gpus:
    try:
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu, True)
        print(f"Found {len(gpus)} GPU(s)")
    except RuntimeError as e:
        print(e)

def load_data():
    """Load và chuẩn bị dữ liệu"""
    print("Đang đọc dữ liệu...")
    
    # Kiểm tra file tồn tại
    train_path = 'sign_mnist_train/sign_mnist_train.csv'
    test_path = 'sign_mnist_test/sign_mnist_test.csv'
    
    if not os.path.exists(train_path):
        raise FileNotFoundError(f"Không tìm thấy file: {train_path}")
    if not os.path.exists(test_path):
        raise FileNotFoundError(f"Không tìm thấy file: {test_path}")
    
    train_df = pd.read_csv(train_path)
    test_df = pd.read_csv(test_path)
    
    print(f"Training data shape: {train_df.shape}")
    print(f"Test data shape: {test_df.shape}")
    
    return train_df, test_df

def prepare_data(df):
    """Chuẩn bị dữ liệu cho training"""
    labels = df['label'].values
    pixels = df.drop('label', axis=1).values
    
    # Reshape thành ảnh 28x28
    images = pixels.reshape(-1, 28, 28, 1)
    
    # Normalize
    images = images.astype('float32') / 255.0
    
    return images, labels

def create_model(input_shape, num_classes):
    """Tạo CNN model"""
    model = keras.Sequential([
        # First Convolutional Block
        layers.Conv2D(32, (3, 3), activation='relu', input_shape=input_shape),
        layers.BatchNormalization(),
        layers.Conv2D(32, (3, 3), activation='relu'),
        layers.MaxPooling2D((2, 2)),
        layers.Dropout(0.25),
        
        # Second Convolutional Block
        layers.Conv2D(64, (3, 3), activation='relu'),
        layers.BatchNormalization(),
        layers.Conv2D(64, (3, 3), activation='relu'),
        layers.MaxPooling2D((2, 2)),
        layers.Dropout(0.25),
        
        # Third Convolutional Block
        layers.Conv2D(128, (3, 3), activation='relu'),
        layers.BatchNormalization(),
        layers.Dropout(0.25),
        
        # Dense layers
        layers.Flatten(),
        layers.Dense(512, activation='relu'),
        layers.BatchNormalization(),
        layers.Dropout(0.5),
        layers.Dense(256, activation='relu'),
        layers.Dropout(0.5),
        layers.Dense(num_classes, activation='softmax')
    ])
    
    return model

def main():
    print("=== Sign Language Recognition Model Training ===")
    
    # Load data
    train_df, test_df = load_data()
    
    # Prepare data
    X_train, y_train = prepare_data(train_df)
    X_test, y_test = prepare_data(test_df)
    
    # Create validation split
    X_train_split, X_val, y_train_split, y_val = train_test_split(
        X_train, y_train, test_size=0.2, random_state=42, stratify=y_train
    )
    
    print(f"Training set: {X_train_split.shape[0]} samples")
    print(f"Validation set: {X_val.shape[0]} samples")
    print(f"Test set: {X_test.shape[0]} samples")
    
    # Model parameters
    num_classes = len(np.unique(y_train))
    print(f"Number of classes: {num_classes}")
    
    # Create model
    model = create_model((28, 28, 1), num_classes)
    model.summary()
    
    # Compile model
    model.compile(
        optimizer='adam',
        loss='sparse_categorical_crossentropy',
        metrics=['accuracy']
    )
    
    # Callbacks
    callbacks = [
        keras.callbacks.EarlyStopping(
            monitor='val_loss',
            patience=10,
            restore_best_weights=True
        ),
        keras.callbacks.ReduceLROnPlateau(
            monitor='val_loss',
            factor=0.2,
            patience=5,
            min_lr=0.0001
        ),
        keras.callbacks.ModelCheckpoint(
            'best_sign_model.h5',
            monitor='val_accuracy',
            save_best_only=True,
            verbose=1
        )
    ]
    
    # Train model
    print("\nBắt đầu training...")
    history = model.fit(
        X_train_split, y_train_split,
        batch_size=32,
        epochs=30,  # Giảm epochs để test nhanh
        validation_data=(X_val, y_val),
        callbacks=callbacks,
        verbose=1
    )
    
    # Evaluate on test set
    print("\nĐánh giá model trên test set...")
    test_loss, test_accuracy = model.evaluate(X_test, y_test, verbose=0)
    print(f"Test Accuracy: {test_accuracy:.4f}")
    print(f"Test Loss: {test_loss:.4f}")
    
    # Save model
    model.save('sign_language_model.h5')
    print("Đã lưu model: sign_language_model.h5")
    
    # Label mapping
    label_map = {
        0: 'A', 1: 'B', 2: 'C', 3: 'D', 4: 'E', 5: 'F', 6: 'G', 7: 'H', 8: 'I',
        10: 'K', 11: 'L', 12: 'M', 13: 'N', 14: 'O', 15: 'P', 16: 'Q', 17: 'R',
        18: 'S', 19: 'T', 20: 'U', 21: 'V', 22: 'W', 23: 'X', 24: 'Y'
    }
    
    # Save label mapping
    with open('label_mapping.pkl', 'wb') as f:
        pickle.dump(label_map, f)
    print("Đã lưu label mapping: label_mapping.pkl")
    
    # Save model info
    model_info = {
        'input_shape': (28, 28, 1),
        'num_classes': num_classes,
        'test_accuracy': test_accuracy,
        'label_mapping': label_map
    }
    
    with open('model_info.pkl', 'wb') as f:
        pickle.dump(model_info, f)
    print("Đã lưu model info: model_info.pkl")
    
    print(f"\n=== Training hoàn thành! ===")
    print(f"Test Accuracy: {test_accuracy:.4f}")
    print("Bạn có thể chạy web app bằng: python app.py")

if __name__ == "__main__":
    main()
