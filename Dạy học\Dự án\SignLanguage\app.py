from flask import Flask, render_template, request, jsonify, Response
from flask_cors import CORS
import cv2
import numpy as np
import tensorflow as tf
import mediapipe as mp
import pickle
import base64
import io
from PIL import Image
import json

app = Flask(__name__)
CORS(app)

# Global variables
model = None
label_mapping = None
hand_detector = None

class HandDetector:
    def __init__(self):
        self.mp_hands = mp.solutions.hands
        self.hands = self.mp_hands.Hands(
            static_image_mode=False,
            max_num_hands=1,
            min_detection_confidence=0.7,
            min_tracking_confidence=0.5
        )
        self.mp_draw = mp.solutions.drawing_utils
    
    def detect_hands(self, image):
        rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        results = self.hands.process(rgb_image)
        return results
    
    def extract_hand_region(self, image, results):
        if results.multi_hand_landmarks:
            hand_landmarks = results.multi_hand_landmarks[0]
            
            h, w, _ = image.shape
            x_coords = [landmark.x * w for landmark in hand_landmarks.landmark]
            y_coords = [landmark.y * h for landmark in hand_landmarks.landmark]
            
            x_min, x_max = int(min(x_coords)), int(max(x_coords))
            y_min, y_max = int(min(y_coords)), int(max(y_coords))
            
            # Add padding
            padding = 20
            x_min = max(0, x_min - padding)
            y_min = max(0, y_min - padding)
            x_max = min(w, x_max + padding)
            y_max = min(h, y_max + padding)
            
            hand_region = image[y_min:y_max, x_min:x_max]
            return hand_region, (x_min, y_min, x_max, y_max)
        
        return None, None

def load_model_and_mappings():
    global model, label_mapping, hand_detector
    
    try:
        # Load model
        model = tf.keras.models.load_model('sign_language_model.h5')
        print("Model loaded successfully!")
        
        # Load label mapping
        with open('label_mapping.pkl', 'rb') as f:
            label_mapping = pickle.load(f)
        print("Label mapping loaded successfully!")
        
        # Initialize hand detector
        hand_detector = HandDetector()
        print("Hand detector initialized!")
        
        return True
    except Exception as e:
        print(f"Error loading model: {e}")
        return False

def preprocess_for_prediction(hand_region):
    if hand_region is None:
        return None
    
    # Convert to grayscale
    gray = cv2.cvtColor(hand_region, cv2.COLOR_BGR2GRAY)
    
    # Resize to 28x28
    resized = cv2.resize(gray, (28, 28))
    
    # Normalize
    normalized = resized.astype('float32') / 255.0
    
    # Reshape for model input
    processed = normalized.reshape(1, 28, 28, 1)
    
    return processed

def predict_sign(processed_image):
    if processed_image is None or model is None:
        return None, 0
    
    prediction = model.predict(processed_image, verbose=0)
    predicted_class = np.argmax(prediction)
    confidence = np.max(prediction)
    
    predicted_letter = label_mapping.get(predicted_class, 'Unknown')
    
    return predicted_letter, confidence

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/predict', methods=['POST'])
def predict():
    try:
        # Get image data from request
        data = request.get_json()
        image_data = data['image']
        
        # Decode base64 image
        image_data = image_data.split(',')[1]  # Remove data:image/jpeg;base64,
        image_bytes = base64.b64decode(image_data)
        
        # Convert to OpenCV format
        nparr = np.frombuffer(image_bytes, np.uint8)
        image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        # Detect hands
        results = hand_detector.detect_hands(image)
        hand_region, bbox = hand_detector.extract_hand_region(image, results)
        
        if hand_region is not None:
            # Preprocess and predict
            processed_image = preprocess_for_prediction(hand_region)
            predicted_letter, confidence = predict_sign(processed_image)
            
            return jsonify({
                'success': True,
                'prediction': predicted_letter,
                'confidence': float(confidence),
                'hand_detected': True
            })
        else:
            return jsonify({
                'success': True,
                'prediction': None,
                'confidence': 0,
                'hand_detected': False,
                'message': 'No hand detected'
            })
            
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/health')
def health():
    return jsonify({
        'status': 'healthy',
        'model_loaded': model is not None,
        'label_mapping_loaded': label_mapping is not None
    })

if __name__ == '__main__':
    print("Starting Sign Language Recognition Web App...")
    
    # Load model and mappings
    if load_model_and_mappings():
        print("All components loaded successfully!")
        app.run(debug=True, host='0.0.0.0', port=5000)
    else:
        print("Failed to load required components. Please train the model first.")
